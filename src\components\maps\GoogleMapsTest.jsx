/**
 * Simple Google Maps Test Component
 * Used to debug Google Maps API issues
 */

import React, { useEffect, useRef, useState } from 'react';
import { Box, Typography, Alert, Button, CircularProgress } from '@mui/material';

const GOOGLE_MAPS_API_KEY = 'AIzaSyDqJtH6hpF1i1ct9qHzKsqHh4wzMwZTzfw';
const DEFAULT_CENTER = { lat: 3.8480, lng: 11.5021 }; // Yaoundé, Cameroon

function GoogleMapsTest() {
  const mapRef = useRef(null);
  const [map, setMap] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [apiLoaded, setApiLoaded] = useState(false);

  // Check if Google Maps API is loaded
  useEffect(() => {
    const checkGoogleMaps = () => {
      if (window.google && window.google.maps) {
        console.log('✅ Google Maps API is loaded');
        setApiLoaded(true);
        setLoading(false);
        return true;
      }
      return false;
    };

    // Check immediately
    if (checkGoogleMaps()) return;

    // Load Google Maps API if not loaded
    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=${GOOGLE_MAPS_API_KEY}&libraries=places,geometry`;
    script.async = true;
    script.defer = true;
    
    script.onload = () => {
      console.log('📍 Google Maps script loaded');
      if (checkGoogleMaps()) {
        console.log('✅ Google Maps API ready');
      } else {
        setError('Google Maps API failed to initialize after script load');
        setLoading(false);
      }
    };
    
    script.onerror = (e) => {
      console.error('❌ Failed to load Google Maps script:', e);
      setError('Failed to load Google Maps script. Check your API key and internet connection.');
      setLoading(false);
    };

    document.head.appendChild(script);

    return () => {
      // Cleanup script if component unmounts
      if (document.head.contains(script)) {
        document.head.removeChild(script);
      }
    };
  }, []);

  // Initialize map when API is ready
  useEffect(() => {
    if (apiLoaded && mapRef.current && !map) {
      try {
        console.log('🗺️ Initializing map...');
        const mapInstance = new window.google.maps.Map(mapRef.current, {
          center: DEFAULT_CENTER,
          zoom: 12,
          mapTypeId: 'roadmap'
        });

        // Add a marker
        new window.google.maps.Marker({
          position: DEFAULT_CENTER,
          map: mapInstance,
          title: 'Yaoundé, Cameroon'
        });

        setMap(mapInstance);
        console.log('✅ Map initialized successfully');
      } catch (err) {
        console.error('❌ Error initializing map:', err);
        setError(`Map initialization error: ${err.message}`);
      }
    }
  }, [apiLoaded, map]);

  const testApiKey = async () => {
    try {
      const response = await fetch(`https://maps.googleapis.com/maps/api/geocode/json?address=Yaoundé&key=${GOOGLE_MAPS_API_KEY}`);
      const data = await response.json();
      
      if (data.status === 'OK') {
        console.log('✅ API Key is valid');
        alert('API Key is valid!');
      } else {
        console.error('❌ API Key error:', data.status, data.error_message);
        alert(`API Key error: ${data.status} - ${data.error_message || 'Unknown error'}`);
      }
    } catch (err) {
      console.error('❌ API Key test failed:', err);
      alert(`API Key test failed: ${err.message}`);
    }
  };

  if (loading) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <CircularProgress />
        <Typography sx={{ mt: 2 }}>Loading Google Maps API...</Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          <Typography variant="h6" gutterBottom>Google Maps Error</Typography>
          <Typography>{error}</Typography>
        </Alert>
        <Button variant="contained" onClick={testApiKey}>
          Test API Key
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h5" gutterBottom>
        Google Maps Test
      </Typography>
      
      <Box sx={{ mb: 2, display: 'flex', gap: 2 }}>
        <Button variant="contained" onClick={testApiKey}>
          Test API Key
        </Button>
        <Typography variant="body2" sx={{ alignSelf: 'center' }}>
          API Status: {apiLoaded ? '✅ Loaded' : '❌ Not Loaded'}
        </Typography>
      </Box>

      <Box
        ref={mapRef}
        sx={{
          width: '100%',
          height: 400,
          border: '1px solid #ccc',
          borderRadius: 1
        }}
      />
      
      {map && (
        <Typography variant="body2" sx={{ mt: 2, color: 'success.main' }}>
          ✅ Map initialized successfully!
        </Typography>
      )}
    </Box>
  );
}

export default GoogleMapsTest;
